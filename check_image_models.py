#!/usr/bin/env python3
"""
检查图片生成模型的支持方法
"""

from google import gena<PERSON>

def check_image_generation_models():
    """检查图片生成模型"""
    print("🔍 检查图片生成模型的支持方法...")
    print("=" * 60)
    
    try:
        # 创建客户端
        client = genai.Client(api_key="AIzaSyDw3Z1JLckTkAc47hxVb8PuDiIr80zSbbs")
        
        # 获取模型列表
        models = list(client.models.list())
        
        # 查找图片相关模型
        image_models = []
        for model in models:
            if any(keyword in model.name.lower() for keyword in ['imagen', 'image', 'vision']):
                image_models.append(model)
        
        print(f"📋 找到 {len(image_models)} 个图片相关模型:\n")
        
        for model in image_models:
            print(f"🖼️ 模型: {model.name}")
            print(f"   显示名称: {model.display_name}")
            print(f"   版本: {model.version}")
            
            # 检查支持的方法
            if hasattr(model, 'supported_generation_methods'):
                methods = model.supported_generation_methods
                print(f"   支持的方法: {methods if methods else '无'}")
            else:
                print("   支持的方法: 未知")
            
            # 检查输入输出限制
            if hasattr(model, 'input_token_limit'):
                print(f"   输入限制: {model.input_token_limit} tokens")
            if hasattr(model, 'output_token_limit'):
                print(f"   输出限制: {model.output_token_limit} tokens")
            
            print()
        
        # 测试一些常用的文本生成模型是否支持图片生成
        print("🧪 测试常用模型的图片生成能力:")
        test_models = [
            "gemini-2.0-flash-exp",
            "gemini-2.5-flash", 
            "gemini-1.5-pro-latest"
        ]
        
        for model_name in test_models:
            print(f"\n🔬 测试模型: {model_name}")
            try:
                # 尝试简单的文本生成
                response = client.models.generate_content(
                    model=model_name,
                    contents="请描述一只戴着帽子的飞猪"
                )
                print(f"   ✅ 文本生成成功: {response.text[:50]}...")
                
                # 尝试图片生成请求
                try:
                    image_response = client.models.generate_content(
                        model=model_name,
                        contents="Generate an image of a pig with wings and a top hat"
                    )
                    print(f"   🖼️ 图片生成请求成功")
                    
                    # 检查响应内容
                    if image_response.candidates:
                        candidate = image_response.candidates[0]
                        if candidate.content and candidate.content.parts:
                            has_image = False
                            for part in candidate.content.parts:
                                if hasattr(part, 'inline_data') and part.inline_data:
                                    has_image = True
                                    break
                            if has_image:
                                print(f"   🎉 模型支持图片生成！")
                            else:
                                print(f"   📝 只返回文本: {image_response.text[:50]}...")
                        
                except Exception as e:
                    print(f"   ❌ 图片生成失败: {str(e)[:100]}...")
                    
            except Exception as e:
                print(f"   ❌ 模型测试失败: {str(e)[:100]}...")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_image_generation_models()
