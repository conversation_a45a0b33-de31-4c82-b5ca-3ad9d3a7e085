#!/usr/bin/env python3
"""
演示版本 - 模拟 OpenAI API 响应
用于测试代码结构和逻辑，无需真实的 API 密钥
"""

import os
import sys
from dotenv import load_dotenv
load_dotenv()

# 模拟的响应类
class MockResponse:
    def __init__(self, output):
        self.output = output

class MockAgent:
    def __init__(self, model, system_prompt):
        self.model = model
        self.system_prompt = system_prompt
        print(f"✅ 创建模拟代理，系统提示: {system_prompt[:50]}...")
    
    def run_sync(self, question):
        """模拟同步运行"""
        print(f"📤 发送问题: {question}")
        
        # 模拟不同问题的响应
        responses = {
            "你好，请介绍一下你自己。": "你好！我是一个AI助手，基于大语言模型构建，能够帮助您解答问题、提供信息和协助完成各种任务。我很高兴为您服务！",
            "请用中文解释什么是人工智能？": "人工智能（Artificial Intelligence，简称AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。这包括学习、推理、感知、理解自然语言、识别模式等能力。AI技术广泛应用于语音识别、图像处理、自然语言处理、机器学习等领域。",
            "请告诉我今天的日期。": "抱歉，作为演示版本，我无法获取实时日期信息。在真实环境中，我可以通过系统调用获取当前日期。"
        }
        
        # 模拟处理延迟
        import time
        time.sleep(1)  # 模拟网络延迟
        
        response_text = responses.get(question, f"这是对问题'{question}'的模拟回复。在真实环境中，这将由OpenAI API生成。")
        return MockResponse(response_text)

def create_mock_provider(api_key: str, base_url: str, proxy_url: str = None):
    """创建模拟 Provider"""
    print(f"🔧 创建模拟 Provider")
    print(f"   API Key: {'*' * (len(api_key) - 8) + api_key[-8:] if len(api_key) > 8 else '***'}")
    print(f"   Base URL: {base_url}")
    if proxy_url:
        print(f"   Proxy: {proxy_url}")
    return "MockProvider"

def run_conversation(agent) -> None:
    """运行对话测试"""
    questions = [
        "你好，请介绍一下你自己。",
        "请用中文解释什么是人工智能？",
        "请告诉我今天的日期。"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\n🤖 对话 {i}...")
        try:
            result = agent.run_sync(question)
            print(f"✅ AI 回复: {result.output}")
        except Exception as e:
            print(f"❌ 对话失败: {e}")

def check_environment() -> tuple[str, str, str]:
    """检查环境变量配置"""
    api_key = os.getenv("OPENAI_API_KEY", "demo-key-for-testing")
    base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
    proxy_url = os.getenv("PROXY_URL")
    
    print(f"🔍 环境配置检查:")
    print(f"   API Key: {'✅ 已设置' if api_key else '❌ 未设置'}")
    print(f"   Base URL: {base_url}")
    if proxy_url:
        print(f"   Proxy URL: {proxy_url}")
    else:
        print(f"   Proxy URL: 未设置")
    
    return api_key, base_url, proxy_url

def main() -> None:
    print("🚀 Pydantic-AI 演示程序")
    print("=" * 50)
    print("📝 注意: 这是演示版本，使用模拟响应")
    print("   要使用真实API，请在 .env 文件中配置正确的 OPENAI_API_KEY")
    print("=" * 50)
    
    # 检查环境配置
    api_key, base_url, proxy_url = check_environment()
    
    # 创建模型和代理
    model_name = "gpt-3.5-turbo"
    print(f"\n🤖 使用模型: {model_name}")
    
    try:
        # 创建模拟的模型和代理
        provider = create_mock_provider(api_key, base_url, proxy_url)
        print(f"✅ Provider 创建成功: {provider}")
        
        agent = MockAgent(
            model=f"Mock-{model_name}",
            system_prompt="You are a helpful assistant that can perform various tasks. Please respond in Chinese when the user asks questions in Chinese."
        )
        
        print("\n🎬 开始对话演示...")
        run_conversation(agent)
        
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    print("\n🎉 演示完成！")
    print("\n📋 下一步:")
    print("1. 获取有效的 OpenAI API 密钥")
    print("2. 在 .env 文件中配置 OPENAI_API_KEY")
    print("3. 运行 main.py 或 main_improved.py 使用真实API")

if __name__ == "__main__":
    main()
