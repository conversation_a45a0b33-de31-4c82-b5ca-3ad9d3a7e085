#!/usr/bin/env python3
"""
图片生成程序测试版本
模拟API调用，不需要实际的Google API密钥
"""

import os
import sys
from io import BytesIO

def check_dependencies():
    """检查所需的依赖包"""
    missing_packages = []
    
    try:
        from google import genai
        from google.genai import types
        print("✅ google-genai 已安装")
        genai_available = True
    except ImportError:
        missing_packages.append("google-genai")
        print("❌ google-genai 未安装")
        genai_available = False
    
    try:
        from PIL import Image
        print("✅ pillow 已安装")
        pil_available = True
    except ImportError:
        missing_packages.append("pillow")
        print("❌ pillow 未安装")
        pil_available = False
    
    return genai_available, pil_available, missing_packages

def create_mock_image():
    """创建一个模拟的生成图片"""
    try:
        from PIL import Image, ImageDraw
        
        # 创建一个模拟的AI生成图片
        img = Image.new('RGB', (512, 512), color='skyblue')
        draw = ImageDraw.Draw(img)
        
        # 绘制一个简单的场景来模拟"带翅膀和帽子的猪飞过未来城市"
        
        # 地面/城市底部
        draw.rectangle([0, 400, 512, 512], fill='darkgreen')
        
        # 未来城市建筑
        buildings = [
            ([50, 300, 100, 400], 'gray'),
            ([120, 250, 170, 400], 'lightgray'),
            ([190, 280, 240, 400], 'darkgray'),
            ([260, 200, 310, 400], 'silver'),
            ([330, 270, 380, 400], 'gray'),
            ([400, 220, 450, 400], 'lightgray'),
        ]
        
        for building, color in buildings:
            draw.rectangle(building, fill=color)
            # 添加窗户
            for y in range(building[1] + 20, building[3] - 10, 30):
                for x in range(building[0] + 10, building[2] - 10, 20):
                    draw.rectangle([x, y, x+8, y+8], fill='yellow')
        
        # 天空中的云朵
        draw.ellipse([100, 50, 180, 100], fill='white')
        draw.ellipse([300, 80, 400, 130], fill='white')
        
        # 飞行的猪（简化版）
        pig_x, pig_y = 250, 150
        
        # 猪的身体
        draw.ellipse([pig_x-30, pig_y-20, pig_x+30, pig_y+20], fill='pink')
        
        # 猪的头
        draw.ellipse([pig_x+20, pig_y-15, pig_x+50, pig_y+15], fill='pink')
        
        # 猪的鼻子
        draw.ellipse([pig_x+35, pig_y-5, pig_x+45, pig_y+5], fill='darkpink')
        
        # 翅膀
        draw.ellipse([pig_x-40, pig_y-30, pig_x-10, pig_y-10], fill='white')
        draw.ellipse([pig_x-40, pig_y+10, pig_x-10, pig_y+30], fill='white')
        
        # 帽子（简化的圆顶帽）
        draw.ellipse([pig_x+15, pig_y-35, pig_x+55, pig_y-15], fill='black')
        draw.rectangle([pig_x+20, pig_y-25, pig_x+50, pig_y-20], fill='black')
        
        # 添加一些绿色植物在建筑上
        for i in range(5):
            x = 80 + i * 80
            draw.ellipse([x, 280-i*10, x+20, 300-i*10], fill='green')
        
        # 添加标题
        draw.text((10, 10), "模拟AI生成: 带翅膀和帽子的猪飞过未来城市", fill='black')
        
        return img
        
    except Exception as e:
        print(f"❌ 创建模拟图片失败: {e}")
        return None

def simulate_api_call():
    """模拟Google Gemini API调用"""
    print("🤖 模拟发送图片生成请求...")
    
    # 模拟的提示词
    prompt = ('Hi, can you create a 3d rendered image of a pig '
             'with wings and a top hat flying over a happy '
             'futuristic scifi city with lots of greenery?')
    
    print(f"📝 提示词: {prompt}")
    print("⏳ 模拟API处理中...")
    
    # 模拟API响应
    mock_response = {
        'text': '我为您创建了一张3D渲染图片，展示了一只戴着礼帽、长着翅膀的可爱小猪，正在一座充满绿色植物的未来科幻城市上空飞翔。这座城市有着现代化的建筑和丰富的绿化，营造出一种快乐和希望的氛围。',
        'has_image': True
    }
    
    return mock_response

def main():
    """主函数"""
    print("🖼️ Google Gemini 图片生成程序 (测试版)")
    print("=" * 60)
    
    # 检查依赖
    genai_available, pil_available, missing_packages = check_dependencies()
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("正在运行模拟模式...")
    
    if not pil_available:
        print("❌ 无法创建图片，需要安装 pillow")
        print("请运行: pip install pillow")
        return
    
    try:
        # 模拟API调用
        response = simulate_api_call()
        
        print("✅ 收到模拟响应:")
        print(f"📝 AI 回复: {response['text']}")
        
        if response['has_image']:
            print("🖼️ 创建模拟生成的图片...")
            
            # 创建模拟图片
            mock_image = create_mock_image()
            
            if mock_image:
                # 保存图片
                output_filename = 'gemini-mock-image.png'
                mock_image.save(output_filename)
                print(f"💾 模拟图片已保存为: {output_filename}")
                
                # 尝试显示图片
                try:
                    mock_image.show()
                    print("🖼️ 模拟图片已在默认查看器中打开")
                except Exception as e:
                    print(f"⚠️ 无法自动打开图片: {e}")
                    print("您可以手动打开文件查看图片")
                
                print("🎉 模拟图片生成完成！")
            else:
                print("❌ 模拟图片创建失败")
        
        print("\n" + "=" * 60)
        print("📋 说明:")
        print("这是一个测试版本，使用模拟数据而不是真实的API调用")
        print("要使用真实的Google Gemini API，请:")
        print("1. 安装所需包: pip install google-genai pillow")
        print("2. 获取Google API密钥并配置")
        print("3. 运行 main_image_gen.py")
        
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
