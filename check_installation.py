#!/usr/bin/env python3
"""
检查 Python 环境和包安装状态
"""

import sys
import subprocess

print("🔍 检查 Python 环境和包安装状态")
print("=" * 50)

# 1. Python 版本和路径
print(f"Python 版本: {sys.version}")
print(f"Python 可执行文件路径: {sys.executable}")
print(f"Python 路径: {sys.path[:3]}...")  # 只显示前3个路径

# 2. 检查 Flask
print("\n📦 检查 Flask:")
try:
    import flask
    print(f"✅ Flask 已安装")
    try:
        print(f"   版本: {flask.__version__}")
    except:
        import importlib.metadata
        print(f"   版本: {importlib.metadata.version('flask')}")
    print(f"   安装路径: {flask.__file__}")
except ImportError as e:
    print(f"❌ Flask 未安装: {e}")

# 3. 检查 Google GenAI
print("\n🤖 检查 Google GenAI:")
try:
    from google import genai
    print(f"✅ Google GenAI 已安装")
    print(f"   安装路径: {genai.__file__}")
except ImportError as e:
    print(f"❌ Google GenAI 未安装: {e}")

# 4. 使用 pip 检查已安装的包
print("\n📋 使用 pip 检查相关包:")
try:
    result = subprocess.run([sys.executable, "-m", "pip", "list"], 
                          capture_output=True, text=True, timeout=30)
    if result.returncode == 0:
        lines = result.stdout.split('\n')
        relevant_packages = [line for line in lines if any(pkg in line.lower() 
                           for pkg in ['flask', 'google', 'genai'])]
        if relevant_packages:
            for pkg in relevant_packages:
                print(f"   {pkg}")
        else:
            print("   未找到相关包")
    else:
        print(f"   pip list 执行失败: {result.stderr}")
except Exception as e:
    print(f"   pip list 执行出错: {e}")

# 5. 测试简单的 Flask 应用
print("\n🧪 测试简单的 Flask 应用:")
try:
    from flask import Flask
    app = Flask(__name__)
    
    @app.route('/')
    def hello():
        return "Hello World!"
    
    # 测试应用创建
    with app.test_client() as client:
        response = client.get('/')
        print(f"✅ Flask 应用测试成功: {response.status_code}")
        print(f"   响应内容: {response.get_data(as_text=True)}")
        
except Exception as e:
    print(f"❌ Flask 应用测试失败: {e}")

print("\n" + "=" * 50)
print("🎯 检查完成")
