#!/usr/bin/env python3
"""
图片生成程序调试版本
逐步检查和测试各个组件
"""

import os
import sys

def test_python_environment():
    """测试Python环境"""
    print("🐍 Python 环境测试")
    print("=" * 40)
    print(f"Python 版本: {sys.version}")
    print(f"Python 路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    return True

def test_basic_imports():
    """测试基本导入"""
    print("\n📦 测试基本Python模块导入")
    print("=" * 40)
    
    basic_modules = ['os', 'sys', 'io', 'base64']
    for module in basic_modules:
        try:
            __import__(module)
            print(f"✅ {module}: 可用")
        except ImportError:
            print(f"❌ {module}: 不可用")
    
    return True

def test_pip_availability():
    """测试pip是否可用"""
    print("\n🔧 测试pip可用性")
    print("=" * 40)
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ pip 可用: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ pip 不可用: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ pip 测试失败: {e}")
        return False

def test_package_installation():
    """测试包安装"""
    print("\n📋 检查所需包的安装状态")
    print("=" * 40)
    
    packages = {
        'PIL': 'pillow',
        'google.genai': 'google-genai'
    }
    
    installed = []
    missing = []
    
    for import_name, package_name in packages.items():
        try:
            __import__(import_name)
            print(f"✅ {package_name}: 已安装")
            installed.append(package_name)
        except ImportError:
            print(f"❌ {package_name}: 未安装")
            missing.append(package_name)
    
    return installed, missing

def attempt_pillow_install():
    """尝试安装pillow"""
    print("\n🔄 尝试安装pillow")
    print("=" * 40)
    
    try:
        import subprocess
        print("正在安装pillow...")
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', 'pillow'], 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ pillow 安装成功")
            print(result.stdout)
            return True
        else:
            print("❌ pillow 安装失败")
            print(result.stderr)
            return False
    except subprocess.TimeoutExpired:
        print("⏰ 安装超时")
        return False
    except Exception as e:
        print(f"❌ 安装过程出错: {e}")
        return False

def test_image_creation():
    """测试图片创建功能"""
    print("\n🖼️ 测试图片创建功能")
    print("=" * 40)
    
    try:
        from PIL import Image, ImageDraw
        print("✅ PIL 导入成功")
        
        # 创建一个简单的测试图片
        img = Image.new('RGB', (200, 100), color='lightblue')
        draw = ImageDraw.Draw(img)
        draw.text((10, 40), "Test Image", fill='black')
        
        # 保存图片
        test_filename = 'debug_test_image.png'
        img.save(test_filename)
        print(f"✅ 测试图片已保存: {test_filename}")
        
        # 尝试显示图片
        try:
            img.show()
            print("✅ 图片显示成功")
        except Exception as e:
            print(f"⚠️ 图片显示失败: {e}")
        
        return True
        
    except ImportError:
        print("❌ PIL 未安装，无法创建图片")
        return False
    except Exception as e:
        print(f"❌ 图片创建失败: {e}")
        return False

def analyze_original_code():
    """分析原始代码"""
    print("\n🔍 分析原始代码")
    print("=" * 40)
    
    try:
        with open('main_image_gen.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("✅ 成功读取 main_image_gen.py")
        
        # 检查关键组件
        checks = [
            ('google.genai 导入', 'from google import genai' in content),
            ('PIL 导入', 'from PIL import Image' in content),
            ('API密钥设置', 'AIzaSyDw3Z1JLckTkAc47hxVb8PuDiIr80zSbbs' in content),
            ('模型调用', 'gemini-2.0-flash-preview-image-generation' in content),
            ('图片保存', 'image.save' in content),
        ]
        
        for check_name, result in checks:
            status = "✅" if result else "❌"
            print(f"{status} {check_name}: {'存在' if result else '缺失'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取代码失败: {e}")
        return False

def provide_recommendations():
    """提供建议"""
    print("\n💡 调试建议")
    print("=" * 40)
    print("基于测试结果，建议按以下步骤操作：")
    print()
    print("1. 确保网络连接正常")
    print("2. 尝试手动安装依赖:")
    print("   python -m pip install --upgrade pip")
    print("   python -m pip install pillow")
    print("   python -m pip install google-genai")
    print()
    print("3. 如果网络较慢，可以使用国内镜像:")
    print("   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple pillow")
    print("   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple google-genai")
    print()
    print("4. 验证API密钥是否有效")
    print("5. 检查防火墙设置")

def main():
    """主函数"""
    print("🔧 图片生成程序调试工具")
    print("=" * 50)
    
    # 逐步测试各个组件
    test_python_environment()
    test_basic_imports()
    
    pip_available = test_pip_availability()
    installed, missing = test_package_installation()
    
    # 如果pillow未安装且pip可用，尝试安装
    if 'pillow' in missing and pip_available:
        if attempt_pillow_install():
            # 重新检查安装状态
            installed, missing = test_package_installation()
    
    # 如果pillow已安装，测试图片创建
    if 'pillow' not in missing:
        test_image_creation()
    
    analyze_original_code()
    provide_recommendations()
    
    print("\n" + "=" * 50)
    print("🎯 调试总结")
    if len(missing) == 0:
        print("✅ 所有依赖都已安装，可以尝试运行原程序")
    else:
        print(f"⚠️ 还需要安装: {', '.join(missing)}")

if __name__ == "__main__":
    main()
