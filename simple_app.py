#!/usr/bin/env python3
"""
简化版 Flask 应用测试
"""

from flask import Flask, jsonify

app = Flask(__name__)

@app.route('/')
def hello():
    return """
    <h1>🎉 Flask 应用运行成功！</h1>
    <p>如果您看到这个页面，说明 Flask 工作正常。</p>
    <p><a href="/test">测试 API</a></p>
    """

@app.route('/test')
def test():
    return jsonify({
        "status": "success",
        "message": "API 工作正常",
        "flask_version": "3.1.1"
    })

if __name__ == '__main__':
    print("🚀 启动简化版 Flask 应用...")
    print("🌐 访问地址: http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=True)
