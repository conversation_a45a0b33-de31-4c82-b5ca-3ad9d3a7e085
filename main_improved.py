import os
import sys
from dotenv import load_dotenv
load_dotenv()

import httpx
from openai import AsyncOpenAI
from pydantic_ai.models.openai import OpenAIModel
from pydantic_ai import Agent
from pydantic_ai.providers.openai import OpenAIProvider

def create_provider(api_key: str, base_url: str, proxy_url: str = None) -> OpenAIProvider:
    """创建 OpenAI Provider"""
    try:
        if proxy_url:
            print(f"🌐 使用代理: {proxy_url}")
            http_client = httpx.AsyncClient(proxy=proxy_url, timeout=60.0, verify=True)
            client = AsyncOpenAI(base_url=base_url, api_key=api_key, http_client=http_client)
            return OpenAIProvider(openai_client=client)
        else:
            print(f"🔗 直连 API: {base_url}")
            return OpenAIProvider(base_url=base_url, api_key=api_key)
    except Exception as e:
        print(f"❌ 创建 Provider 失败: {e}")
        sys.exit(1)

def run_conversation(agent: Agent) -> None:
    """运行对话测试"""
    questions = [
        "你好，请介绍一下你自己。",
        "请用中文解释什么是人工智能？",
        "请告诉我今天的日期。"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\n🤖 对话 {i}: {question}")
        try:
            result = agent.run_sync(question)
            print(f"✅ AI 回复: {result.output}")
        except Exception as e:
            print(f"❌ 对话失败: {e}")

def check_environment() -> tuple[str, str, str]:
    """检查环境变量配置"""
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
    proxy_url = os.getenv("PROXY_URL")
    
    if not api_key:
        print("❌ 错误: 未找到 OPENAI_API_KEY 环境变量")
        print("请在 .env 文件中设置您的 OpenAI API 密钥")
        sys.exit(1)
    
    print(f"✅ API Key: {'*' * (len(api_key) - 8) + api_key[-8:]}")
    print(f"✅ Base URL: {base_url}")
    if proxy_url:
        print(f"✅ Proxy URL: {proxy_url}")
    
    return api_key, base_url, proxy_url

def main() -> None:
    print("🚀 启动 Pydantic-AI 对话测试程序")
    print("=" * 50)
    
    # 检查环境配置
    api_key, base_url, proxy_url = check_environment()
    
    # 创建模型和代理
    model_name = "gpt-3.5-turbo"
    print(f"🤖 使用模型: {model_name}")
    
    try:
        model = OpenAIModel(
            model_name, 
            provider=create_provider(api_key, base_url, proxy_url)
        )
        agent = Agent(
            model=model,
            system_prompt="You are a helpful assistant that can perform various tasks. Please respond in Chinese when the user asks questions in Chinese."
        )
        
        print("✅ 代理创建成功，开始对话测试...")
        run_conversation(agent)
        
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        sys.exit(1)
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
