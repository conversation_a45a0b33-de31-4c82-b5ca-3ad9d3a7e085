import os
import sys
from io import BytesIO
# from dotenv import load_dotenv

# 加载环境变量
# load_dotenv()

try:
    from google import genai
    from google.genai import types
    from PIL import Image
    import PIL.Image
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请安装所需的包:")
    print("pip install -r requirements_image_gen.txt")
    sys.exit(1)

def create_sample_image():
    """创建一个示例图片用于测试"""
    # 创建一个简单的测试图片
    img = Image.new('RGB', (300, 200), color='lightblue')
    img.save('sample_image.png')
    print("✅ 创建了示例图片: sample_image.png")
    return 'sample_image.png'

def main():
    print("🖼️ Google GenAI 图片生成测试程序")
    print("=" * 50)

    # 检查API密钥
    # api_key = os.getenv('GOOGLE_API_KEY')
    api_key = os.getenv('from google import genai
from google.genai import types
from PIL import Image
from io import BytesIO

import PIL.Image

image = PIL.Image.open('/path/to/image.png')

client = genai.Client()

text_input = ('Hi, This is a picture of me.'
            'Can you add a llama next to me?',)

response = client.models.generate_content(
    model="gemini-2.0-flash-preview-image-generation",
    contents=[text_input, image],
    config=types.GenerateContentConfig(
      response_modalities=['TEXT', 'IMAGE']
    )
)

for part in response.candidates[0].content.parts:
  if part.text is not None:
    print(part.text)
  elif part.inline_data is not None:
    image = Image.open(BytesIO((part.inline_data.data)))
    image.show()')
    if not api_key:
        print("❌ 错误: 未找到 GOOGLE_API_KEY 环境变量")
        print("请设置您的 Google API 密钥:")
        print("export GOOGLE_API_KEY='your-api-key-here'")
        print("或在 .env 文件中添加: GOOGLE_API_KEY=your-api-key-here")
        return

    try:
        # 检查是否有图片文件，如果没有则创建一个
        image_path = 'sample_image.png'
        if not os.path.exists(image_path):
            image_path = create_sample_image()

        # 加载图片
        print(f"📁 加载图片: {image_path}")
        image = PIL.Image.open(image_path)

        # 创建客户端
        print("🔗 连接到 Google GenAI...")
        client = genai.Client(api_key=api_key)

        # 修正文本输入（移除多余的逗号）
        text_input = 'Hi, This is a picture of me. Can you add a llama next to me?'

        print("🤖 发送图片生成请求...")
        print(f"📝 提示词: {text_input}")

        # 生成内容
        response = client.models.generate_content(
            model="gemini-2.0-flash-preview-image-generation",
            contents=[text_input, image],
            config=types.GenerateContentConfig(
                response_modalities=['TEXT', 'IMAGE']
            )
        )

        print("✅ 收到响应，处理结果...")

        # 检查响应是否有效
        if not response or not response.candidates:
            print("❌ 未收到有效响应")
            return

        candidate = response.candidates[0]
        if not candidate or not candidate.content or not candidate.content.parts:
            print("❌ 响应中没有内容")
            return

        # 处理响应
        for i, part in enumerate(candidate.content.parts):
            if part.text is not None:
                print(f"📝 文本响应 {i+1}: {part.text}")
            elif part.inline_data is not None and part.inline_data.data is not None:
                print(f"🖼️ 生成图片 {i+1}")
                try:
                    image_data = part.inline_data.data
                    if isinstance(image_data, bytes):
                        generated_image = Image.open(BytesIO(image_data))
                        output_filename = f'generated_image_{i+1}.png'
                        generated_image.save(output_filename)
                        print(f"💾 图片已保存为: {output_filename}")

                        # 尝试显示图片（如果支持的话）
                        try:
                            generated_image.show()
                            print("🖼️ 图片已在默认查看器中打开")
                        except Exception as e:
                            print(f"⚠️ 无法自动打开图片: {e}")
                    else:
                        print(f"⚠️ 图片数据格式不正确: {type(image_data)}")
                except Exception as e:
                    print(f"❌ 处理生成图片时出错: {e}")

        print("🎉 图片生成完成！")

    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请安装所需的包:")
        print("pip install google-genai pillow")
    except Exception as e:
        print(f"❌ 运行错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()