#!/usr/bin/env python3
"""
列出可用的 Google Gemini 模型
"""

from google import genai

def list_available_models():
    """列出所有可用的模型"""
    print("🔍 查询可用的 Google Gemini 模型...")
    print("=" * 60)
    
    try:
        # 创建客户端
        client = genai.Client(api_key="AIzaSyDw3Z1JLckTkAc47hxVb8PuDiIr80zSbbs")
        
        # 获取模型列表
        models = list(client.models.list())

        print(f"📋 找到 {len(models)} 个可用模型:\n")

        for i, model in enumerate(models, 1):
            print(f"{i:2d}. 模型名称: {model.name}")
            print(f"    显示名称: {model.display_name}")
            print(f"    版本: {model.version}")
            
            # 显示支持的方法
            if hasattr(model, 'supported_generation_methods'):
                methods = model.supported_generation_methods
                print(f"    支持的方法: {', '.join(methods) if methods else '无'}")
            
            # 显示输入/输出模态
            if hasattr(model, 'input_token_limit'):
                print(f"    输入限制: {model.input_token_limit} tokens")
            if hasattr(model, 'output_token_limit'):
                print(f"    输出限制: {model.output_token_limit} tokens")
                
            print()
        
        # 查找图片生成相关的模型
        print("🖼️ 图片生成相关模型:")
        image_models = []
        for model in models:
            if any(keyword in model.name.lower() for keyword in ['image', 'vision', 'multimodal']):
                image_models.append(model)
                print(f"   • {model.name} - {model.display_name}")
        
        if not image_models:
            print("   ❌ 未找到明确的图片生成模型")
            print("   💡 可能需要使用通用模型如 gemini-2.0-flash-exp")
        
    except Exception as e:
        print(f"❌ 获取模型列表失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    list_available_models()
