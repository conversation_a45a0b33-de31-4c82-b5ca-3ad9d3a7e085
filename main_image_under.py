from google import genai
from google.genai import types

client = genai.Client(api_key="AIzaSyDw3Z1JLckTkAc47hxVb8PuDiIr80zSbbs")


with open('./gemini-native-image-1.png', 'rb') as f:
    image_bytes = f.read()

response = client.models.generate_content(
model='gemini-2.5-flash',
contents=[
    types.Part.from_bytes(
    data=image_bytes,
    mime_type='image/jpeg',
    ),
    'Caption this image.'
]
)

print(response.text)