#!/usr/bin/env python3
"""
调试 Flask 导入问题
"""

print("🔍 开始调试 Flask 导入...")

# 1. 检查 Python 版本
import sys
print(f"Python 版本: {sys.version}")

# 2. 检查 Flask 导入
try:
    from flask import Flask
    print("✅ Flask 导入成功")
except ImportError as e:
    print(f"❌ Flask 导入失败: {e}")
    exit(1)

# 3. 检查 Flask 版本
try:
    import flask
    print(f"Flask 版本: {flask.__version__}")
except:
    print("无法获取 Flask 版本")

# 4. 检查其他导入
try:
    from flask import render_template, request, jsonify
    print("✅ Flask 子模块导入成功")
except ImportError as e:
    print(f"❌ Flask 子模块导入失败: {e}")

# 5. 检查 Google GenAI
try:
    from google import genai
    print("✅ Google GenAI 导入成功")
except ImportError as e:
    print(f"❌ Google GenAI 导入失败: {e}")

# 6. 创建简单的 Flask 应用测试
try:
    app = Flask(__name__)
    
    @app.route('/')
    def hello():
        return "Hello Flask!"
    
    print("✅ Flask 应用创建成功")
    
    # 测试运行（不实际启动服务器）
    with app.test_client() as client:
        response = client.get('/')
        print(f"✅ 测试请求成功: {response.status_code}")
        
except Exception as e:
    print(f"❌ Flask 应用创建失败: {e}")

print("🎯 调试完成")
