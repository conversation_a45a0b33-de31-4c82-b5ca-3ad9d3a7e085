#!/usr/bin/env python3
"""
Flask Web应用 - Google Gemini 聊天界面
基于 main_text_gen.py 创建的网页版聊天窗口
"""

from flask import Flask, render_template, request, jsonify
import os
import sys
from datetime import datetime

app = Flask(__name__)

# 检查依赖
def check_dependencies():
    """检查所需的依赖包"""
    try:
        from google import genai
        return True, None
    except ImportError as e:
        return False, str(e)

# 创建Gemini客户端
def create_gemini_client():
    """创建Google Gemini客户端"""
    try:
        from google import genai
        # 使用您在main_text_gen.py中的API密钥
        client = genai.Client(api_key="AIzaSyDw3Z1JLckTkAc47hxVb8PuDiIr80zSbbs")
        return client, None
    except Exception as e:
        return None, str(e)

@app.route('/')
def index():
    """主页面"""
    # 检查依赖
    deps_ok, deps_error = check_dependencies()
    return render_template('index.html', deps_ok=deps_ok, deps_error=deps_error)

@app.route('/api/chat', methods=['POST'])
def chat():
    """聊天API端点"""
    try:
        data = request.get_json()
        user_message = data.get('message', '').strip()
        
        if not user_message:
            return jsonify({'error': '消息不能为空'}), 400
        
        # 检查依赖
        deps_ok, deps_error = check_dependencies()
        if not deps_ok:
            return jsonify({
                'error': f'依赖包未安装: {deps_error}',
                'suggestion': '请运行: pip install google-genai'
            }), 500
        
        # 创建客户端
        client, client_error = create_gemini_client()
        if not client:
            return jsonify({'error': f'客户端创建失败: {client_error}'}), 500
        
        # 调用Gemini API
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents=user_message
        )
        
        return jsonify({
            'response': response.text,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
        
    except Exception as e:
        return jsonify({'error': f'处理请求时出错: {str(e)}'}), 500

@app.route('/api/status')
def status():
    """检查系统状态"""
    deps_ok, deps_error = check_dependencies()
    
    if deps_ok:
        client, client_error = create_gemini_client()
        client_ok = client is not None
    else:
        client_ok = False
        client_error = "依赖包未安装"
    
    return jsonify({
        'dependencies': deps_ok,
        'client': client_ok,
        'error': deps_error or client_error,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    })

if __name__ == '__main__':
    print("🚀 启动 Google Gemini 聊天Web应用")
    print("=" * 50)
    
    # 检查依赖
    deps_ok, deps_error = check_dependencies()
    if not deps_ok:
        print(f"⚠️ 警告: {deps_error}")
        print("请运行: pip install google-genai flask")
        print("应用仍会启动，但聊天功能可能不可用")
    else:
        print("✅ 依赖检查通过")
    
    print("\n🌐 访问地址: http://localhost:5000")
    print("按 Ctrl+C 停止服务器")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
