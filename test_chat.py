#!/usr/bin/env python3
"""
测试聊天功能的脚本
"""

import requests
import json
import time

def test_chat_api():
    """测试聊天API"""
    print("🧪 测试 Google Gemini 聊天API")
    print("=" * 50)
    
    # 测试服务器状态
    try:
        print("1. 检查服务器状态...")
        status_response = requests.get("http://localhost:5000/api/status", timeout=10)
        if status_response.status_code == 200:
            status_data = status_response.json()
            print(f"   ✅ 服务器状态: {status_data}")
        else:
            print(f"   ❌ 状态检查失败: {status_response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 无法连接到服务器: {e}")
        print("   💡 请确保运行了: python app.py")
        return
    
    # 测试聊天功能
    test_messages = [
        "你好！",
        "请介绍一下自己",
        "什么是人工智能？",
        "用中文回答：今天天气怎么样？"
    ]
    
    print("\n2. 测试聊天功能...")
    for i, message in enumerate(test_messages, 1):
        print(f"\n   测试 {i}: 发送消息 '{message}'")
        
        try:
            # 发送聊天请求
            chat_data = {"message": message}
            chat_response = requests.post(
                "http://localhost:5000/api/chat",
                json=chat_data,
                timeout=30
            )
            
            if chat_response.status_code == 200:
                response_data = chat_response.json()
                ai_reply = response_data.get('response', '无回复')
                timestamp = response_data.get('timestamp', '无时间戳')
                
                print(f"   ✅ AI回复: {ai_reply[:100]}{'...' if len(ai_reply) > 100 else ''}")
                print(f"   🕒 时间: {timestamp}")
            else:
                error_data = chat_response.json() if chat_response.headers.get('content-type') == 'application/json' else {}
                print(f"   ❌ 请求失败: {chat_response.status_code}")
                print(f"   错误信息: {error_data.get('error', '未知错误')}")
                
        except requests.exceptions.Timeout:
            print(f"   ⏰ 请求超时 (30秒)")
        except Exception as e:
            print(f"   ❌ 请求出错: {e}")
        
        # 避免请求过于频繁
        if i < len(test_messages):
            time.sleep(1)
    
    print("\n" + "=" * 50)
    print("🎯 测试完成！")
    print("\n💡 如果测试成功，您可以在浏览器中访问:")
    print("   http://localhost:5000")

def test_direct_genai():
    """直接测试GenAI功能"""
    print("\n🔬 直接测试 Google GenAI")
    print("=" * 50)
    
    try:
        from google import genai
        
        # 创建客户端
        client = genai.Client(api_key="AIzaSyDw3Z1JLckTkAc47hxVb8PuDiIr80zSbbs")
        
        # 测试调用
        response = client.models.generate_content(
            model="gemini-2.5-flash",
            contents="请用中文简单介绍一下自己"
        )
        
        print(f"✅ 直接调用成功:")
        print(f"   AI回复: {response.text}")
        
    except Exception as e:
        print(f"❌ 直接调用失败: {e}")

if __name__ == "__main__":
    # 先测试直接调用
    test_direct_genai()
    
    # 再测试Web API
    test_chat_api()
