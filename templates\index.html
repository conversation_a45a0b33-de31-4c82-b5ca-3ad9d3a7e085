<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Gemini 聊天助手</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff4444;
        }

        .status-indicator.online {
            background: #44ff44;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            word-wrap: break-word;
            position: relative;
        }

        .message.user .message-content {
            background: #4285f4;
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.ai .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 5px;
        }

        .message-time {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 5px;
        }

        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .chat-input-form {
            display: flex;
            gap: 10px;
        }

        .chat-input {
            flex: 1;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }

        .chat-input:focus {
            border-color: #4285f4;
        }

        .send-button {
            padding: 15px 25px;
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }

        .send-button:hover:not(:disabled) {
            background: #3367d6;
        }

        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border-left: 4px solid #c62828;
        }

        .welcome-message {
            text-align: center;
            color: #666;
            padding: 40px 20px;
        }

        .welcome-message h2 {
            margin-bottom: 10px;
            color: #4285f4;
        }

        @media (max-width: 600px) {
            .chat-container {
                width: 95%;
                height: 95vh;
                border-radius: 10px;
            }
            
            .message-content {
                max-width: 85%;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>🤖 Google Gemini 聊天助手</h1>
            <p>基于 main_text_gen.py 的网页版聊天界面</p>
            <div class="status-indicator" id="statusIndicator"></div>
        </div>

        <div class="chat-messages" id="chatMessages">
            {% if not deps_ok %}
            <div class="error-message">
                <strong>⚠️ 依赖包未安装:</strong> {{ deps_error }}<br>
                请运行: <code>pip install google-genai flask</code>
            </div>
            {% endif %}
            
            <div class="welcome-message">
                <h2>👋 欢迎使用 Gemini 聊天助手</h2>
                <p>这是基于您的 main_text_gen.py 代码创建的网页版聊天界面</p>
                <p>请在下方输入您的问题，我会使用 Google Gemini AI 为您回答</p>
            </div>
        </div>

        <div class="loading" id="loading">
            <p>🤔 AI 正在思考中...</p>
        </div>

        <div class="chat-input-container">
            <form class="chat-input-form" id="chatForm">
                <input 
                    type="text" 
                    class="chat-input" 
                    id="messageInput" 
                    placeholder="输入您的问题..." 
                    autocomplete="off"
                    required
                >
                <button type="submit" class="send-button" id="sendButton">
                    发送
                </button>
            </form>
        </div>
    </div>

    <script>
        const chatMessages = document.getElementById('chatMessages');
        const chatForm = document.getElementById('chatForm');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const loading = document.getElementById('loading');
        const statusIndicator = document.getElementById('statusIndicator');

        // 检查系统状态
        async function checkStatus() {
            try {
                const response = await fetch('/api/status');
                const data = await response.json();
                
                if (data.dependencies && data.client) {
                    statusIndicator.classList.add('online');
                    statusIndicator.title = '系统正常';
                } else {
                    statusIndicator.classList.remove('online');
                    statusIndicator.title = `系统错误: ${data.error}`;
                }
            } catch (error) {
                statusIndicator.classList.remove('online');
                statusIndicator.title = '连接错误';
            }
        }

        // 添加消息到聊天窗口
        function addMessage(content, isUser = false, timestamp = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'ai'}`;
            
            const now = timestamp || new Date().toLocaleString('zh-CN');
            
            messageDiv.innerHTML = `
                <div class="message-content">
                    ${content}
                    <div class="message-time">${now}</div>
                </div>
            `;
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 发送消息
        async function sendMessage(message) {
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();

                if (response.ok) {
                    addMessage(data.response, false, data.timestamp);
                } else {
                    addMessage(`❌ 错误: ${data.error}${data.suggestion ? '<br>💡 ' + data.suggestion : ''}`, false);
                }
            } catch (error) {
                addMessage(`❌ 网络错误: ${error.message}`, false);
            }
        }

        // 表单提交处理
        chatForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const message = messageInput.value.trim();
            if (!message) return;

            // 添加用户消息
            addMessage(message, true);
            
            // 清空输入框并禁用
            messageInput.value = '';
            messageInput.disabled = true;
            sendButton.disabled = true;
            loading.style.display = 'block';

            // 发送消息
            await sendMessage(message);

            // 重新启用输入
            messageInput.disabled = false;
            sendButton.disabled = false;
            loading.style.display = 'none';
            messageInput.focus();
        });

        // 页面加载时检查状态
        checkStatus();
        
        // 定期检查状态
        setInterval(checkStatus, 30000);

        // 自动聚焦输入框
        messageInput.focus();
    </script>
</body>
</html>
