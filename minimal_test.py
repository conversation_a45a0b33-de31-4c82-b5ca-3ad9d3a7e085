#!/usr/bin/env python3

print("开始测试...")

try:
    print("1. 测试 Flask 导入...")
    from flask import Flask
    print("   ✅ Flask 导入成功")
    
    print("2. 创建 Flask 应用...")
    app = Flask(__name__)
    print("   ✅ Flask 应用创建成功")
    
    print("3. 定义路由...")
    @app.route('/')
    def hello():
        return "Hello World!"
    print("   ✅ 路由定义成功")
    
    print("4. 测试应用...")
    with app.test_client() as client:
        response = client.get('/')
        print(f"   ✅ 测试成功: {response.status_code}")
    
    print("5. 启动服务器...")
    print("   🌐 访问 http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=True)
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请运行: pip install flask")
except Exception as e:
    print(f"❌ 其他错误: {e}")
